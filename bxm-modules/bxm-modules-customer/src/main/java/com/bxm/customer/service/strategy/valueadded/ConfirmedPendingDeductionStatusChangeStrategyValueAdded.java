package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.strategy.ValueAddedAbstractStatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 已确认待扣款状态变更策略
 *
 * 处理从"已确认待扣款"状态到其他状态的转换变更
 *
 * 支持的状态转换：
 * 1. CONFIRMED_PENDING_DEDUCTION -> DEDUCTION_COMPLETED (扣款正常完成)
 * 2. CONFIRMED_PENDING_DEDUCTION -> DEDUCTION_EXCEPTION (扣款异常)
 * 3. CONFIRMED_PENDING_DEDUCTION -> PENDING_CONFIRMATION (退回待确认)
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Slf4j
@Component
public class ConfirmedPendingDeductionStatusChangeStrategyValueAdded extends ValueAddedAbstractStatusChangeStrategy {

    @Override
    public boolean supports(ValueAddedDeliveryOrderStatus currentStatus, ValueAddedDeliveryOrderStatus targetStatus) {
        if (currentStatus != ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION) {
            return false;
        }

        // 支持的目标状态
        return targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED ||
               targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION ||
               targetStatus == ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION;
    }

    @Override
    protected void validateSpecificTransition(ValueAddedDeliveryOrder order,
                                            StatusChangeRequestDTO request,
                                            ValueAddedDeliveryOrderStatus targetStatus) {
        if (targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED) {
            // 验证扣款完成
            validateDeductionCompleted(order, request);
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.DEDUCTION_EXCEPTION) {
            // 验证扣款异常
            validateDeductionException(order, request);
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION) {
            // 验证退回待确认
            validateReturnToConfirmation(order, request);
        } else {
            throwUnsupportedTransition("已确认待扣款", request.getTargetStatus());
        }
    }

    @Override
    public ValueAddedDeliveryOrderStatus getSupportedCurrentStatus() {
        return ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION;
    }

    /**
     * 验证扣款完成
     */
    private void validateDeductionCompleted(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 使用抽象基类的通用验证方法
        validateOperatorId(request.getOperatorId());
        validateCustomerInfo(order);
        validateCreditCode(order);
        validateTaxpayerType(order);

        // 验证扣款相关信息
        if (order.getSyncHandlingFee() == null) {
            throw new IllegalArgumentException("是否同步手续费标志不能为空");
        }

        logValidationPassed("Deduction completion", request.getDeliveryOrderNo());
    }

    /**
     * 验证扣款异常
     */
    private void validateDeductionException(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 使用抽象基类的通用验证方法
        validateExceptionOperation(request, "扣款", 10);
        logValidationPassed("Deduction exception", request.getDeliveryOrderNo());
    }

    /**
     * 验证退回待确认状态
     */
    private void validateReturnToConfirmation(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 使用抽象基类的通用验证方法
        validateReturnOperation(request, "待确认状态");
        logValidationPassed("Return to confirmation", request.getDeliveryOrderNo());
    }
}
