package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.ValueAddedAccountPeriodService;
import com.bxm.customer.service.strategy.ValueAddedAbstractStatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 已提交待交付状态变更策略
 *
 * 处理从"已提交待交付"状态到其他状态的转换变更
 *
 * 支持的状态转换：
 * 1. SUBMITTED_PENDING_DELIVERY -> PENDING_CONFIRMATION (正常交付完成)
 * 2. SUBMITTED_PENDING_DELIVERY -> DELIVERY_EXCEPTION (交付异常)
 * 3. SUBMITTED_PENDING_DELIVERY -> SAVED_PENDING_SUBMIT (退回待提交)
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Slf4j
@Component
public class SubmittedPendingDeliveryStatusChangeStrategyValueAdded extends ValueAddedAbstractStatusChangeStrategy {

    @Autowired
    private ValueAddedAccountPeriodService valueAddedAccountPeriodService;

    @Override
    public boolean supports(ValueAddedDeliveryOrderStatus currentStatus, ValueAddedDeliveryOrderStatus targetStatus) {
        if (currentStatus != ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY) {
            return false;
        }

        // 支持的目标状态
        return targetStatus == ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION ||
               targetStatus == ValueAddedDeliveryOrderStatus.DELIVERY_EXCEPTION ||
               targetStatus == ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT;
    }

    @Override
    protected void validateSpecificTransition(ValueAddedDeliveryOrder order,
                                            StatusChangeRequestDTO request,
                                            ValueAddedDeliveryOrderStatus targetStatus) {
        if (targetStatus == ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION) {
            // 验证交付完成
            validateDeliveryCompleted(order, request);
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.DELIVERY_EXCEPTION) {
            // 验证交付异常
            validateDeliveryException(order, request);
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT) {
            // 验证退回待提交
            validateReturnToPendingSubmit(order, request);
        } else {
            throwUnsupportedTransition("已提交待交付", request.getTargetStatus());
        }
    }

    @Override
    public ValueAddedDeliveryOrderStatus getSupportedCurrentStatus() {
        return ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY;
    }

    /**
     * 验证交付完成
     */
    private void validateDeliveryCompleted(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 使用抽象基类的通用验证方法
        validateOperatorId(request.getOperatorId());
        validateNotEmpty(order.getRequirements(), "交付要求");

        // 验证业务部门信息
        if (order.getBusinessDeptId() == null) {
            throw new IllegalArgumentException("业务部门ID不能为空");
        }

        logValidationPassed("Delivery completion", request.getDeliveryOrderNo());
    }

    /**
     * 验证交付异常
     */
    private void validateDeliveryException(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 使用抽象基类的通用验证方法
        validateExceptionOperation(request, "交付", 10);
        logValidationPassed("Delivery exception", request.getDeliveryOrderNo());
    }

    /**
     * 验证退回待提交状态
     */
    private void validateReturnToPendingSubmit(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        // 使用抽象基类的通用验证方法
        validateReturnOperation(request, "待提交状态");

        // 删除增值账期记录
        try {
            valueAddedAccountPeriodService.deleteValueAddedPeriodRecords(order);
            log.info("ValueAddedPeriodMonth records deleted successfully for order: {}", request.getDeliveryOrderNo());
        } catch (Exception e) {
            log.error("Failed to delete ValueAddedPeriodMonth records for order: {}", request.getDeliveryOrderNo(), e);
            throw new RuntimeException("删除增值账期记录失败: " + e.getMessage(), e);
        }

        logValidationPassed("Return to pending submit", request.getDeliveryOrderNo());
    }
}
